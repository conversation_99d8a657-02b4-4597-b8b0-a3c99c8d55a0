import request from '../../utils/request';

/**
 * 查询量化进化量数据
 * @param params 查询参数
 */
export function queryCreditEvolvePages(params: {
  phone?: string;
  startDate?: string;
  endDate?: string;
  pageNum: number;
  pageSize: number;
}) {
  return request({
    url: '/mall-project/api/queryCreditEvolvePages',
    method: 'post',
    data: params
  });
}

/**
 * 导出信用进化量Excel
 * @param data 查询参数
 */
export const exportCreditEvolveExcel = (data: any) => {
    return request({
        url: '/mall-project/api/exportCreditEvolveExcel',
        method: 'post',
        data: data,
        responseType: 'blob'
    });
};

// 注释：原来的独立统计接口已经合并到 queryCreditEvolvePages 接口中
// 统计数据现在通过 queryCreditEvolvePages 接口的 summary 字段返回
// todayTotalCreditEvolve: 今日总量化进化量
// totalCreditEvolve: 累计量化进化量