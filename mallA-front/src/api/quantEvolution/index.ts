import request from '../../utils/request';

/**
 * 查询量化值进化量数据
 * @param params 查询参数
 */
export function queryQuantifyEvolvePages(params: {
  phone?: string;
  startDate?: string;
  endDate?: string;
  pageNum: number;
  pageSize: number;
}) {
  return request({
    url: '/mall-project/api/queryQuantifyEvolvePages',
    method: 'post',
    data: params
  });
}

/**
 * 导出量化值进化量Excel
 * @param data 查询参数
 */
export const exportQuantifyEvolveExcel = (data: any) => {
    return request({
        url: '/mall-project/api/exportQuantifyEvolveExcel',
        method: 'post',
        data: data,
        responseType: 'blob'
    });
};

// 注释：原来的独立统计接口已经合并到 queryQuantifyEvolvePages 接口中
// 统计数据现在通过 queryQuantifyEvolvePages 接口的 summary 字段返回
// totalQuantifyEvolve: 今日总量化值进化量
// todayTotalQuantifyEvolve: 累计量化值进化量