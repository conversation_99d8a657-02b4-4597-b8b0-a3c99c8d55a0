import { id } from 'element-plus/es/locale/index.mjs';
import request from '../../utils/request'
const FORM_CONTENT_TYPE = 'application/x-www-form-urlencoded';
//渲染图表数据
export const getQuantizationRate = (data)=>{
    return request({
        url:'/mall-project/cooperateEnterprise/getQuantizationRate',
        method: 'POST',
        data
    })
}

//获取量化数分页数据
export const QueryQuantifyCountPages = (data) => {
    return request({
        url: '/mall-project/api/QueryQuantifyCountPages',
        method: 'POST',
        data
    })
}

//导出量化数Excel
export const exportQuantifyCountExcel = (data) => {
    return request({
        url: '/mall-project/api/exportQuantifyCountExcel',
        method: 'post',
        data,
        responseType: 'blob'
    })
}

//获取量化数设置状态
export const getQuantifyCount = () => {
    return request({
        url: '/mall-project/api/getQuantifyCount',
        method: 'GET'
    })
}

//保存或更新量化数设置
export const saveOrUpdateQuantifyCount = (data) => {
    return request({
        url: '/mall-project/api/saveOrUpdateQuantifyCount',
        method: 'PUT',
        data
    })
}

//获取Admin的每日累计量化数
export const getAdminDailyQuantity = () => {
    return request({
        url: '/mall-project/getAdminDailyQuantity',
        method: 'GET'
    })
}

//获取中南惠C的每日所有ID累计量化数
export const getCweightCountTotal = () => {
    return request({
        url: '/mall-project/CweightCountTotal',
        method: 'GET'
    })
}

//获取中南惠所有B的每日累计量化数
export const getBweightCountTotal = () => {
    return request({
        url: '/mall-project/BweightCountTotal',
        method: 'GET'
    })
}

//获取中南惠系统的每日总累计量化数
export const getSumWeightCountTotal = () => {
    return request({
        url: '/mall-project/sumWeightCountTotal',
        method: 'GET'
    })
}

//获取Admin的累计量化数
export const getAdminTotalQuantity = () => {
    return request({
        url: '/mall-project/adminTotalQuantity',
        method: 'GET'
    })
}

//获取中南惠C的所有ID累计量化数
export const getCweightCountTotalAllDays = () => {
    return request({
        url: '/mall-project/cweightCountTotalAllDays',
        method: 'GET'
    })
}

//获取中南惠所有B的累计量化数
export const getBweightCountTotalAlldays = () => {
    return request({
        url: '/mall-project/bweightCountTotalAlldays',
        method: 'GET'
    })
}

//获取中南惠系统的总累计量化数
export const getSumWeightCountTotalAllDays = () => {
    return request({
        url: '/mall-project/sumWeightCountTotalAllDays',
        method: 'GET'
    })
}