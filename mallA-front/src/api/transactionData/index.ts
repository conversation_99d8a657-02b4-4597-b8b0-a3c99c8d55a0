import request from '../../utils/request';

/**
 * 查询交易数据分页列表
 * @param data 查询参数
 */
export const queryZNHTradeDataPages = (data: any) => {
    return request({
        url: '/mall-project/cooperateEnterprise/QueryZNHTradeDataPages',
        method: 'post',
        data: data
    });
};

/**
 * 查询区域授权数据分页列表
 * @param data 查询参数
 */
export const queryAreaAuthorize = (data: {
    type: string;
    level: string;
    pageNum: number;
    pageSize: number;
}) => {
    return request({
        url: '/mall-project/api/queryAreaAuthorize',
        method: 'post',
        data: data
    });
};

/**
 * 查询累计交易数据分页列表
 * @param data 查询参数
 */
export const queryTransactionDataTotalPages = (data: {
    phone: string;
    startDate: string;
    endDate: string;
    pageNum: number;
    pageSize: number;
}) => {
    return request({
        url: '/mall-project/api/QuerytransactionDataTotalPages',
        method: 'post',
        data: data
    });
};

/**
 * 导出累计交易数据Excel
 * @param data 查询参数
 */
export const transactionDataTotalExport = (data: any) => {
    return request({
        url: '/mall-project/api/transactionDataTotalExport',
        method: 'post',
        data: data,
        responseType: 'blob'
    });
};

/**
 * 导出交易数据
 * @param data 查询参数
 */
export const zNHTradeDataExport = (data: any) => {
    return request({
        url: '/mall-project/cooperateEnterprise/zNHTradeDataExport',
        method: 'post',
        data: data,
        responseType: 'blob' // 用于文件下载
    });
};

/**
 * 添加交易数据名称
 * @param data 数据名称
 */
export const tradeDataSet = (data: any) => {
    return request({
        url: '/mall-project/cooperateEnterprise/tradeDataSet',
        method: 'PUT',
        data: data
    });
};

/**
 * 获取交易数据名称列表
 * @param data 查询参数
 */
export const getTradeDataSet = (data?: any) => {
    return request({
        url: '/mall-project/cooperateEnterprise/getTradeDataSet',
        method: 'get',
        params: data
    });
};

/**
 * 删除交易数据名称
 * @param data 包含id的数据
 */
export const deleteTradeDataSet = (data: any) => {
    return request({
        url: '/mall-project/cooperateEnterprise/deleteTradeDataSet',
        method: 'delete',
        data: data
    });
};

/**
 * 保存交易数据参数设置
 * @param data 参数数据
 */
export const tradeDataParameterSet = (data: any) => {
    return request({
        url: '/mall-project/cooperateEnterprise/tradeDataParameterSet',
        method: 'PUT',
        data: data
    });
};

/**
 * 获取交易数据参数设置
 */
export const getTradeDataParameterSet = () => {
    return request({
        url: '/mall-project/cooperateEnterprise/getTradeDataParameterSet',
        method: 'post'
    });
};

/**
 * 删除区域授权数据
 * @param data 包含id的数据
 */
export const deleteAreaAuthorize = (data: { id: number | string }) => {
    return request({
        url: '/mall-project/api/deleteAreaAuthorize',
        method: 'post',
        data: data
    });
};
